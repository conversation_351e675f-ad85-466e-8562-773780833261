# 🧠 Biomedict One

**Biomedict One** is the unified monorepo for all frontend, backend, and shared services powering the Biomedict platform — a next-gen AI-driven ecosystem at the intersection of **wearables**, **mental wellness**, and **physical fitness**.

A Melos-managed project for mono-repo, created using NonStop CLI.

[![nonstop_cli](https://img.shields.io/badge/started%20with-nonstop_cli-166C4E.svg?style=flat-square)](https://pub.dev/packages/nonstop_cli)
[![melos](https://img.shields.io/badge/maintained%20with-melos-f700ff.svg?style=flat-square)](https://github.com/invertase/melos)


## Technologies at play

- Dart
- Flutter
- Melos

## Getting Started

### Prerequisites

- [Flutter SDK](https://flutter.dev/docs/get-started/install) (latest stable version)
- [Dart SDK](https://dart.dev/get-dart) (comes with Flutter)
- [<PERSON><PERSON>](https://melos.invertase.dev/) for monorepo management

### Installation

1. **Install Melos globally:**
   ```bash
   dart pub global activate melos
   ```

2. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd biomedict-one
   ```

3. **Bootstrap the monorepo:**
   ```bash
   melos bootstrap
   ```
   This command will:
   - Install dependencies for all packages
   - Link local packages together
   - Generate necessary files

### Running the App

1. **Navigate to the main app:**
   ```bash
   cd apps/biomedict
   ```

2. **Run the app:**
   ```bash
   flutter run
   ```

### Available Melos Commands

From the root directory, you can run these commands:

- **Clean all packages:**
  ```bash
  melos run clean:flutter
  ```

- **Run linting and analysis:**
  ```bash
  melos run lint
  ```

- **Apply automated fixes:**
  ```bash
  melos run fix
  ```

- **Generate code (build_runner):**
  ```bash
  melos run generate
  ```

- **Run all tests:**
  ```bash
  melos run test
  ```

### Project Structure

- All apps are in the `/apps` directory.
- All features are in the `/features` directory
- All shared packages are in the `/packages` directory
- All plugins are in the `/plugins` directory
- All backend services are in the `/backend` directory