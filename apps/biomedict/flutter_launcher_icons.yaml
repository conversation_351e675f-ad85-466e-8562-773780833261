# flutter pub run flutter_launcher_icons
flutter_launcher_icons:
  image_path: "icon/icon.png"

  android: "launcher_icon"
  # image_path_android: "assets/icon/icon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  # adaptive_icon_background: "assets/icon/background.png"
  # adaptive_icon_foreground: "assets/icon/foreground.png"
  # adaptive_icon_foreground_inset: 16
  # adaptive_icon_monochrome: "assets/icon/monochrome.png"

  ios: true
  # image_path_ios: "assets/icon/icon.png"icon
  remove_alpha_ios: true
  # image_path_ios_dark_transparent: "assets/icon/icon_dark.png"
  # image_path_ios_tinted_grayscale: "assets/icon/icon_tinted.png"
  # desaturate_tinted_to_grayscale_ios: true
  # background_color_ios: "#ffffff"

  web:
    generate: true
    image_path: "icon/icon.png"
    background_color: "#ffffff"
    theme_color: "#545a92"

