import 'package:auth/auth.dart' as auth;
import 'package:core/core.dart' as core;
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class AppRouter {
  /// Create the main application router
  static GoRouter createRouter() {
    return GoRouter(
      initialLocation: core.Routes.signIn,
      routes: [
        GoRoute(
          path: core.Routes.signIn,
          builder: (context, state) => const auth.AppSignInScreen(),
        ),
        GoRoute(
          path: core.Routes.forgotPassword,
          builder: (context, state) {
            final email = state.uri.queryParameters[core.Keys.email];
            return auth.AppForgotPasswordScreen(email: email);
          },
        ),
        // Home Route (placeholder)
        GoRoute(
          path: core.Routes.home,
          builder: (context, state) => const _HomePage(),
        ),
      ],
    );
  }
}

// Placeholder home page until main app screens are implemented
class _HomePage extends StatelessWidget {
  const _HomePage();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        title: Text(
          'BiomeDict',
          style: textTheme.titleLarge?.copyWith(
            color: colorScheme.onPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () {
              // Sign out functionality would be implemented here
              context.go(core.Routes.signIn);
            },
            icon: const Icon(Icons.logout),
          ),
        ],
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.eco_outlined,
              size: 120,
              color: colorScheme.primary,
            ),
            const SizedBox(height: 24),
            Text(
              'Welcome to BiomeDict!',
              style: textTheme.headlineMedium?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Authentication successful.\nMain app features will be implemented here.',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
